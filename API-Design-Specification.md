# API Design Specification - <PERSON><PERSON> thống LMS

## Tổng quan API Architecture

### Base Configuration
- **Base URL**: `https://api.lms.example.com`
- **API Version**: `/api/v1`
- **Content-Type**: `application/json`
- **Authentication**: JWT Bearer Token
- **Rate Limiting**: 1000 requests/hour per user, 10000/hour per admin

### Global Headers
```javascript
{
  "Authorization": "Bearer <jwt_token>",
  "Content-Type": "application/json",
  "Accept": "application/json",
  "X-API-Version": "v1",
  "X-Client-Version": "1.0.0"
}
```

### Standard Response Format
```javascript
{
  "success": boolean,
  "data": object | array | null,
  "message": string,
  "errors": array,
  "meta": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "uuid",
    "pagination": {
      "page": number,
      "limit": number,
      "total": number,
      "totalPages": number
    }
  }
}
```

### Error Response Format
```javascript
{
  "success": false,
  "data": null,
  "message": "Mô tả lỗi chính",
  "errors": [
    {
      "field": "email",
      "code": "INVALID_EMAIL",
      "message": "Email không hợp lệ"
    }
  ],
  "meta": {
    "timestamp": "2024-01-01T00:00:00.000Z",
    "requestId": "uuid"
  }
}
```

---

## 1. USER MANAGEMENT MODULE

### 1.1 Authentication Endpoints

#### POST /api/v1/auth/register
**Mô tả**: Đăng ký tài khoản mới
**Authentication**: None
**Rate Limit**: 5 requests/minute per IP

**Request Body:**
```javascript
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "confirmPassword": "SecurePass123!",
  "profile": {
    "firstName": "Nguyễn",
    "lastName": "Văn A",
    "phone": "+84901234567",
    "dateOfBirth": "1990-01-01"
  },
  "role": "student", // optional, default: "student"
  "acceptTerms": true
}
```

**Response (201 Created):**
```javascript
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "Nguyễn",
        "lastName": "Văn A"
      },
      "role": "student",
      "status": "active",
      "emailVerified": false
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 3600
    }
  },
  "message": "Đăng ký thành công. Vui lòng kiểm tra email để xác thực tài khoản."
}
```

#### POST /api/v1/auth/login
**Mô tả**: Đăng nhập hệ thống
**Authentication**: None
**Rate Limit**: 10 requests/minute per IP

**Request Body:**
```javascript
{
  "email": "<EMAIL>",
  "password": "SecurePass123!",
  "rememberMe": true
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "user": {
      "_id": "user_id",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "Nguyễn",
        "lastName": "Văn A",
        "avatar": "https://cdn.example.com/avatars/user.jpg"
      },
      "role": "student",
      "subscription": {
        "plan": "premium",
        "status": "active",
        "endDate": "2024-12-31T23:59:59.000Z"
      }
    },
    "tokens": {
      "accessToken": "jwt_access_token",
      "refreshToken": "jwt_refresh_token",
      "expiresIn": 3600
    }
  },
  "message": "Đăng nhập thành công"
}
```

#### POST /api/v1/auth/refresh
**Mô tả**: Làm mới access token
**Authentication**: Refresh Token

**Request Body:**
```javascript
{
  "refreshToken": "jwt_refresh_token"
}
```

#### POST /api/v1/auth/logout
**Mô tả**: Đăng xuất (invalidate tokens)
**Authentication**: Required

#### POST /api/v1/auth/forgot-password
**Mô tả**: Quên mật khẩu
**Authentication**: None

**Request Body:**
```javascript
{
  "email": "<EMAIL>"
}
```

#### POST /api/v1/auth/reset-password
**Mô tả**: Đặt lại mật khẩu
**Authentication**: None

**Request Body:**
```javascript
{
  "token": "reset_token_from_email",
  "password": "NewSecurePass123!",
  "confirmPassword": "NewSecurePass123!"
}
```

### 1.2 User Profile Endpoints

#### GET /api/v1/users/profile
**Mô tả**: Lấy thông tin profile người dùng hiện tại
**Authentication**: Required
**Authorization**: Any authenticated user

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "_id": "user_id",
    "email": "<EMAIL>",
    "profile": {
      "firstName": "Nguyễn",
      "lastName": "Văn A",
      "avatar": "https://cdn.example.com/avatars/user.jpg",
      "phone": "+84901234567",
      "dateOfBirth": "1990-01-01",
      "address": {
        "city": "Hồ Chí Minh",
        "country": "Vietnam"
      }
    },
    "role": "student",
    "subscription": {
      "plan": "premium",
      "status": "active",
      "startDate": "2024-01-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.000Z"
    },
    "preferences": {
      "language": "vi",
      "timezone": "Asia/Ho_Chi_Minh",
      "notifications": {
        "email": true,
        "push": true
      }
    },
    "stats": {
      "totalCourses": 5,
      "completedCourses": 2,
      "totalPoints": 1250,
      "currentStreak": 7
    }
  }
}
```

#### PUT /api/v1/users/profile
**Mô tả**: Cập nhật thông tin profile
**Authentication**: Required

**Request Body:**
```javascript
{
  "profile": {
    "firstName": "Nguyễn",
    "lastName": "Văn B",
    "phone": "+84901234568",
    "address": {
      "street": "123 Đường ABC",
      "city": "Hồ Chí Minh",
      "country": "Vietnam"
    }
  },
  "preferences": {
    "language": "en",
    "notifications": {
      "email": false,
      "push": true
    }
  }
}
```

#### POST /api/v1/users/upload-avatar
**Mô tả**: Upload avatar
**Authentication**: Required
**Content-Type**: multipart/form-data

**Request Body:**
```javascript
FormData {
  "avatar": File, // Max 5MB, formats: jpg, png, gif
  "crop": {
    "x": 0,
    "y": 0,
    "width": 200,
    "height": 200
  }
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "avatarUrl": "https://cdn.example.com/avatars/user_new.jpg",
    "thumbnails": {
      "small": "https://cdn.example.com/avatars/user_small.jpg",
      "medium": "https://cdn.example.com/avatars/user_medium.jpg"
    }
  },
  "message": "Avatar đã được cập nhật thành công"
}
```

### 1.3 User Management (Admin Only)

#### GET /api/v1/admin/users
**Mô tả**: Lấy danh sách người dùng (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Query Parameters:**
```javascript
{
  "page": 1,
  "limit": 20,
  "search": "search_term",
  "role": "student|teacher|admin",
  "status": "active|inactive|suspended",
  "subscription": "free|basic|premium",
  "sortBy": "createdAt|lastLogin|email",
  "sortOrder": "asc|desc",
  "dateFrom": "2024-01-01",
  "dateTo": "2024-12-31"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "user_id",
      "email": "<EMAIL>",
      "profile": {
        "firstName": "Nguyễn",
        "lastName": "Văn A",
        "avatar": "https://cdn.example.com/avatars/user.jpg"
      },
      "role": "student",
      "status": "active",
      "subscription": {
        "plan": "premium",
        "status": "active"
      },
      "stats": {
        "totalCourses": 5,
        "lastLogin": "2024-01-15T10:30:00.000Z"
      },
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

#### GET /api/v1/admin/users/:userId
**Mô tả**: Lấy chi tiết người dùng (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

#### PUT /api/v1/admin/users/:userId
**Mô tả**: Cập nhật thông tin người dùng (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Request Body:**
```javascript
{
  "status": "active|inactive|suspended",
  "role": "student|teacher|admin",
  "subscription": {
    "plan": "free|basic|premium",
    "endDate": "2024-12-31T23:59:59.000Z"
  },
  "notes": "Ghi chú của admin"
}
```

#### DELETE /api/v1/admin/users/:userId
**Mô tả**: Xóa người dùng (soft delete)
**Authentication**: Required
**Authorization**: super_admin

---

## 2. COURSE MANAGEMENT MODULE

### 2.1 Public Course Endpoints

#### GET /api/v1/courses
**Mô tả**: Lấy danh sách khóa học công khai
**Authentication**: Optional (for personalized results)

**Query Parameters:**
```javascript
{
  "page": 1,
  "limit": 12,
  "category": "english|japanese|korean|chinese",
  "level": "beginner|intermediate|advanced",
  "priceType": "free|paid",
  "priceMin": 0,
  "priceMax": 1000000,
  "rating": 4.5,
  "search": "search_term",
  "sortBy": "popularity|rating|price|newest",
  "sortOrder": "asc|desc",
  "instructor": "instructor_id"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "course_id",
      "title": "Tiếng Anh Giao Tiếp Cơ Bản",
      "slug": "tieng-anh-giao-tiep-co-ban",
      "shortDescription": "Khóa học tiếng Anh giao tiếp cho người mới bắt đầu",
      "thumbnail": "https://cdn.example.com/courses/course1.jpg",
      "category": "english",
      "level": "beginner",
      "instructor": {
        "name": "Cô Lan Anh",
        "avatar": "https://cdn.example.com/instructors/teacher1.jpg",
        "rating": 4.8
      },
      "pricing": {
        "type": "paid",
        "price": 299000,
        "originalPrice": 399000,
        "currency": "VND",
        "discountPercentage": 25
      },
      "content": {
        "totalLessons": 24,
        "totalDuration": 480,
        "totalQuizzes": 8
      },
      "stats": {
        "totalEnrollments": 1250,
        "averageRating": 4.7,
        "totalRatings": 89,
        "completionRate": 78
      },
      "skills": {
        "listening": 30,
        "speaking": 40,
        "reading": 20,
        "writing": 10
      },
      "isEnrolled": false,
      "progress": null
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 12,
      "total": 45,
      "totalPages": 4
    },
    "filters": {
      "categories": ["english", "japanese"],
      "levels": ["beginner", "intermediate"],
      "priceRange": {
        "min": 0,
        "max": 2000000
      }
    }
  }
}
```

#### GET /api/v1/courses/:courseId
**Mô tả**: Lấy chi tiết khóa học
**Authentication**: Optional

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "_id": "course_id",
    "title": "Tiếng Anh Giao Tiếp Cơ Bản",
    "slug": "tieng-anh-giao-tiep-co-ban",
    "description": "Mô tả chi tiết khóa học...",
    "thumbnail": "https://cdn.example.com/courses/course1.jpg",
    "category": "english",
    "level": "beginner",
    "language": "vi",
    
    "instructor": {
      "_id": "instructor_id",
      "name": "Cô Lan Anh",
      "avatar": "https://cdn.example.com/instructors/teacher1.jpg",
      "bio": "Giảng viên với 10 năm kinh nghiệm...",
      "rating": 4.8,
      "totalStudents": 5000,
      "totalCourses": 12
    },
    
    "pricing": {
      "type": "paid",
      "price": 299000,
      "originalPrice": 399000,
      "currency": "VND"
    },
    
    "content": {
      "totalLessons": 24,
      "totalDuration": 480,
      "totalQuizzes": 8,
      "modules": [
        {
          "_id": "module_id",
          "title": "Module 1: Giới thiệu bản thân",
          "description": "Học cách giới thiệu bản thân bằng tiếng Anh",
          "order": 1,
          "lessons": [
            {
              "_id": "lesson_id",
              "title": "Bài 1: Chào hỏi cơ bản",
              "type": "video",
              "duration": 15,
              "order": 1,
              "isPreview": true,
              "isLocked": false
            }
          ]
        }
      ]
    },
    
    "requirements": [
      "Không cần kiến thức tiền đề",
      "Máy tính hoặc điện thoại có kết nối internet"
    ],
    
    "outcomes": [
      "Có thể giới thiệu bản thân bằng tiếng Anh",
      "Nắm vững 500 từ vựng cơ bản"
    ],
    
    "stats": {
      "totalEnrollments": 1250,
      "averageRating": 4.7,
      "totalRatings": 89,
      "completionRate": 78
    },
    
    "reviews": [
      {
        "userId": "user_id",
        "userName": "Nguyễn Văn A",
        "userAvatar": "https://cdn.example.com/users/user1.jpg",
        "rating": 5,
        "comment": "Khóa học rất hay và dễ hiểu",
        "createdAt": "2024-01-10T00:00:00.000Z"
      }
    ],
    
    "isEnrolled": false,
    "canEnroll": true,
    "enrollmentDeadline": null
  }
}
```

### 2.2 Course Enrollment

#### POST /api/v1/courses/:courseId/enroll
**Mô tả**: Đăng ký khóa học
**Authentication**: Required
**Authorization**: student, teacher

**Request Body:**
```javascript
{
  "paymentMethod": "stripe|paypal|activation_code",
  "activationCode": "ABC123XYZ", // if paymentMethod is activation_code
  "paymentToken": "stripe_token", // if using Stripe
  "couponCode": "DISCOUNT20" // optional
}
```

**Response (201 Created):**
```javascript
{
  "success": true,
  "data": {
    "enrollment": {
      "_id": "enrollment_id",
      "courseId": "course_id",
      "userId": "user_id",
      "status": "enrolled",
      "enrolledAt": "2024-01-15T10:00:00.000Z",
      "expiresAt": null
    },
    "payment": {
      "_id": "payment_id",
      "amount": 299000,
      "status": "completed",
      "method": "stripe"
    }
  },
  "message": "Đăng ký khóa học thành công"
}
```

#### GET /api/v1/courses/:courseId/lessons/:lessonId
**Mô tả**: Lấy nội dung bài học
**Authentication**: Required
**Authorization**: Enrolled students or course instructor

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "_id": "lesson_id",
    "title": "Bài 1: Chào hỏi cơ bản",
    "type": "video",
    "duration": 15,
    "content": {
      "videoUrl": "https://cdn.example.com/videos/lesson1.mp4",
      "subtitles": [
        {
          "language": "vi",
          "url": "https://cdn.example.com/subtitles/lesson1_vi.vtt"
        }
      ],
      "transcript": "Transcript nội dung video...",
      "attachments": [
        {
          "title": "Slide bài học",
          "url": "https://cdn.example.com/files/lesson1_slides.pdf",
          "type": "pdf"
        }
      ]
    },
    "prerequisites": [],
    "resources": [
      {
        "title": "Từ vựng bài học",
        "url": "https://cdn.example.com/resources/vocabulary.pdf",
        "type": "pdf"
      }
    ],
    "nextLesson": {
      "_id": "next_lesson_id",
      "title": "Bài 2: Hỏi thăm sức khỏe"
    },
    "previousLesson": null,
    "progress": {
      "completed": false,
      "timeSpent": 0,
      "lastPosition": 0
    }
  }
}
```

#### POST /api/v1/courses/:courseId/lessons/:lessonId/complete
**Mô tả**: Đánh dấu bài học hoàn thành
**Authentication**: Required

**Request Body:**
```javascript
{
  "timeSpent": 900, // seconds
  "finalPosition": 895, // video position when completed
  "score": 85 // if lesson has quiz
}
```

### 2.3 Course Management (Instructor/Admin)

#### POST /api/v1/courses
**Mô tả**: Tạo khóa học mới
**Authentication**: Required
**Authorization**: teacher, admin

**Request Body:**
```javascript
{
  "title": "Tiếng Anh Giao Tiếp Nâng Cao",
  "description": "Mô tả chi tiết khóa học...",
  "shortDescription": "Mô tả ngắn gọn",
  "category": "english",
  "level": "intermediate",
  "pricing": {
    "type": "paid",
    "price": 499000,
    "currency": "VND"
  },
  "skills": {
    "listening": 25,
    "speaking": 35,
    "reading": 25,
    "writing": 15
  },
  "requirements": ["Đã hoàn thành khóa cơ bản"],
  "outcomes": ["Giao tiếp tự tin trong môi trường công việc"]
}
```

#### PUT /api/v1/courses/:courseId
**Mô tả**: Cập nhật thông tin khóa học
**Authentication**: Required
**Authorization**: Course instructor or admin

#### DELETE /api/v1/courses/:courseId
**Mô tả**: Xóa khóa học
**Authentication**: Required
**Authorization**: Course instructor or admin

#### POST /api/v1/courses/:courseId/modules
**Mô tả**: Thêm module mới vào khóa học
**Authentication**: Required
**Authorization**: Course instructor or admin

**Request Body:**
```javascript
{
  "title": "Module 3: Thảo luận nhóm",
  "description": "Học cách thảo luận nhóm hiệu quả",
  "order": 3
}
```

#### POST /api/v1/courses/:courseId/modules/:moduleId/lessons
**Mô tả**: Thêm bài học vào module
**Authentication**: Required
**Authorization**: Course instructor or admin

**Request Body:**
```javascript
{
  "title": "Bài 1: Kỹ thuật đặt câu hỏi",
  "type": "video",
  "order": 1,
  "content": {
    "videoUrl": "https://cdn.example.com/videos/lesson_new.mp4",
    "transcript": "Nội dung transcript..."
  },
  "isPreview": false
}
```

#### POST /api/v1/courses/:courseId/upload-thumbnail
**Mô tả**: Upload thumbnail cho khóa học
**Authentication**: Required
**Authorization**: Course instructor or admin
**Content-Type**: multipart/form-data

**Request Body:**
```javascript
FormData {
  "thumbnail": File, // Max 10MB, formats: jpg, png
  "alt": "Mô tả hình ảnh"
}
```

---

## 3. ASSESSMENT SYSTEM MODULE

### 3.1 Assessment Management

#### GET /api/v1/assessments
**Mô tả**: Lấy danh sách bài kiểm tra
**Authentication**: Required

**Query Parameters:**
```javascript
{
  "courseId": "course_id", // optional
  "type": "listening|speaking|reading|writing|mixed",
  "level": "beginner|intermediate|advanced",
  "page": 1,
  "limit": 20
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "assessment_id",
      "title": "Kiểm tra Nghe Hiểu - Cấp độ Cơ bản",
      "type": "listening",
      "level": "beginner",
      "duration": 30,
      "totalQuestions": 15,
      "maxAttempts": 3,
      "passingScore": 70,
      "courseId": "course_id",
      "courseName": "Tiếng Anh Giao Tiếp Cơ Bản",
      "stats": {
        "totalAttempts": 245,
        "averageScore": 78.5,
        "passRate": 82
      },
      "userStats": {
        "attempts": 1,
        "bestScore": 85,
        "lastAttempt": "2024-01-10T15:30:00.000Z"
      }
    }
  ]
}
```

#### GET /api/v1/assessments/:assessmentId
**Mô tả**: Lấy chi tiết bài kiểm tra
**Authentication**: Required

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "_id": "assessment_id",
    "title": "Kiểm tra Nghe Hiểu - Cấp độ Cơ bản",
    "type": "listening",
    "level": "beginner",
    "configuration": {
      "duration": 30,
      "maxAttempts": 3,
      "passingScore": 70,
      "randomizeQuestions": true,
      "showResults": "immediate",
      "allowReview": true
    },
    "instructions": "Hướng dẫn làm bài chi tiết...",
    "totalQuestions": 15,
    "skillBreakdown": {
      "listening": 100
    },
    "userProgress": {
      "attempts": 1,
      "bestScore": 85,
      "canAttempt": true,
      "nextAttemptAt": null
    }
  }
}
```

### 3.2 Assessment Taking

#### POST /api/v1/assessments/:assessmentId/start
**Mô tả**: Bắt đầu làm bài kiểm tra
**Authentication**: Required

**Response (201 Created):**
```javascript
{
  "success": true,
  "data": {
    "sessionId": "session_uuid",
    "attemptNumber": 2,
    "startTime": "2024-01-15T14:00:00.000Z",
    "endTime": "2024-01-15T14:30:00.000Z",
    "questions": [
      {
        "_id": "question_id",
        "type": "multiple_choice",
        "skill": "listening",
        "order": 1,
        "points": 2,
        "content": {
          "question": "Nghe đoạn hội thoại và chọn câu trả lời đúng",
          "audioUrl": "https://cdn.example.com/audio/question1.mp3",
          "options": [
            {
              "id": "A",
              "text": "Anh ấy đang đi làm"
            },
            {
              "id": "B",
              "text": "Anh ấy đang đi học"
            }
          ]
        }
      }
    ],
    "timeRemaining": 1800 // seconds
  }
}
```

#### POST /api/v1/assessments/:assessmentId/submit-answer
**Mô tả**: Nộp câu trả lời cho một câu hỏi
**Authentication**: Required

**Request Body:**
```javascript
{
  "sessionId": "session_uuid",
  "questionId": "question_id",
  "response": {
    // For multiple choice
    "selectedOption": "A",

    // For fill in blanks
    "answers": ["answer1", "answer2"],

    // For essay
    "text": "Câu trả lời tự luận...",

    // For speaking
    "audioData": "base64_audio_data",
    "duration": 45
  },
  "timeSpent": 120 // seconds
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "questionId": "question_id",
    "saved": true,
    "autoScore": 2, // if can be auto-scored
    "feedback": "Câu trả lời chính xác!" // if immediate feedback enabled
  }
}
```

#### POST /api/v1/assessments/:assessmentId/submit
**Mô tả**: Nộp bài kiểm tra hoàn chỉnh
**Authentication**: Required

**Request Body:**
```javascript
{
  "sessionId": "session_uuid",
  "responses": [
    {
      "questionId": "question_id",
      "response": {
        "selectedOption": "A"
      },
      "timeSpent": 120
    }
  ],
  "totalTimeSpent": 1650
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "resultId": "result_id",
    "status": "submitted",
    "submittedAt": "2024-01-15T14:27:30.000Z",
    "processingStatus": "scoring", // scoring, completed
    "estimatedResultTime": "2024-01-15T14:30:00.000Z"
  },
  "message": "Bài kiểm tra đã được nộp thành công. Kết quả sẽ có trong vài phút."
}
```

### 3.3 AI Scoring Endpoints

#### POST /api/v1/ai/score-speaking
**Mô tả**: Chấm điểm bài nói bằng AI
**Authentication**: Required (Internal API)
**Authorization**: system

**Request Body:**
```javascript
{
  "audioUrl": "https://cdn.example.com/audio/speaking_response.mp3",
  "questionText": "Describe your daily routine",
  "expectedDuration": 60,
  "criteria": [
    "pronunciation",
    "fluency",
    "grammar",
    "vocabulary",
    "task_response"
  ],
  "level": "intermediate"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "overallScore": 7.5,
    "maxScore": 10,
    "transcription": "I wake up at 6 AM every morning...",
    "duration": 58,
    "criteria": {
      "pronunciation": {
        "score": 8.0,
        "feedback": "Phát âm rõ ràng, chỉ có một vài lỗi nhỏ"
      },
      "fluency": {
        "score": 7.0,
        "feedback": "Nói khá trôi chảy nhưng có một vài khoảng dừng"
      },
      "grammar": {
        "score": 7.5,
        "feedback": "Sử dụng ngữ pháp chính xác trong hầu hết câu"
      },
      "vocabulary": {
        "score": 8.0,
        "feedback": "Từ vựng phong phú và phù hợp với chủ đề"
      },
      "task_response": {
        "score": 7.0,
        "feedback": "Trả lời đúng chủ đề nhưng thiếu một số chi tiết"
      }
    },
    "strengths": [
      "Phát âm rõ ràng",
      "Từ vựng phong phú"
    ],
    "improvements": [
      "Cần cải thiện độ trôi chảy",
      "Thêm chi tiết vào câu trả lời"
    ],
    "confidence": 0.92,
    "processingTime": 3.2
  }
}
```

#### POST /api/v1/ai/score-writing
**Mô tả**: Chấm điểm bài viết bằng AI
**Authentication**: Required (Internal API)

**Request Body:**
```javascript
{
  "text": "My daily routine starts at 6 AM...",
  "prompt": "Describe your daily routine in 150-200 words",
  "criteria": [
    "grammar",
    "vocabulary",
    "coherence",
    "task_response",
    "word_count"
  ],
  "level": "intermediate",
  "expectedWordCount": {
    "min": 150,
    "max": 200
  }
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "overallScore": 8.0,
    "maxScore": 10,
    "wordCount": 178,
    "criteria": {
      "grammar": {
        "score": 8.5,
        "feedback": "Ngữ pháp chính xác, chỉ có 2 lỗi nhỏ",
        "errors": [
          {
            "text": "I goes to work",
            "correction": "I go to work",
            "type": "subject_verb_agreement"
          }
        ]
      },
      "vocabulary": {
        "score": 7.5,
        "feedback": "Từ vựng phù hợp và đa dạng",
        "suggestions": [
          "Có thể sử dụng từ 'commence' thay vì 'start'"
        ]
      },
      "coherence": {
        "score": 8.0,
        "feedback": "Bài viết có cấu trúc rõ ràng và mạch lạc"
      },
      "task_response": {
        "score": 8.5,
        "feedback": "Trả lời đầy đủ yêu cầu của đề bài"
      }
    },
    "strengths": [
      "Cấu trúc bài viết rõ ràng",
      "Nội dung phong phú"
    ],
    "improvements": [
      "Chú ý thỏa thuận chủ ngữ - động từ",
      "Có thể sử dụng từ vựng cao cấp hơn"
    ],
    "confidence": 0.89
  }
}
```

### 3.4 Assessment Results

#### GET /api/v1/assessments/:assessmentId/results
**Mô tả**: Lấy kết quả bài kiểm tra của người dùng
**Authentication**: Required

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "result_id",
      "attemptNumber": 2,
      "status": "graded",
      "results": {
        "totalScore": 85,
        "maxScore": 100,
        "percentage": 85,
        "grade": "B",
        "passed": true,
        "skillBreakdown": {
          "listening": {
            "score": 42,
            "maxScore": 50,
            "percentage": 84
          },
          "speaking": {
            "score": 43,
            "maxScore": 50,
            "percentage": 86,
            "pronunciation": 88,
            "fluency": 82,
            "grammar": 87,
            "vocabulary": 89
          }
        }
      },
      "feedback": {
        "overall": "Kết quả tốt! Bạn đã thể hiện khả năng nghe và nói ở mức độ tốt.",
        "strengths": [
          "Phát âm rõ ràng",
          "Hiểu được nội dung chính"
        ],
        "improvements": [
          "Cần cải thiện độ trôi chảy khi nói",
          "Chú ý đến chi tiết trong bài nghe"
        ],
        "recommendations": [
          "Luyện tập thêm với các bài nghe có tốc độ nhanh",
          "Thực hành nói với native speakers"
        ]
      },
      "submittedAt": "2024-01-15T14:27:30.000Z",
      "gradedAt": "2024-01-15T14:30:15.000Z"
    }
  ]
}
```

#### GET /api/v1/assessments/results/:resultId
**Mô tả**: Lấy chi tiết kết quả một lần làm bài
**Authentication**: Required

#### GET /api/v1/assessments/results/:resultId/review
**Mô tả**: Xem lại bài làm chi tiết (nếu được phép)
**Authentication**: Required

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "allowReview": true,
    "questions": [
      {
        "_id": "question_id",
        "type": "multiple_choice",
        "content": {
          "question": "Nghe đoạn hội thoại và chọn câu trả lời đúng",
          "audioUrl": "https://cdn.example.com/audio/question1.mp3",
          "options": [
            {
              "id": "A",
              "text": "Anh ấy đang đi làm",
              "isCorrect": true
            },
            {
              "id": "B",
              "text": "Anh ấy đang đi học",
              "isCorrect": false
            }
          ]
        },
        "userResponse": {
          "selectedOption": "A"
        },
        "scoring": {
          "points": 2,
          "maxPoints": 2,
          "isCorrect": true,
          "feedback": "Chính xác! Bạn đã hiểu đúng nội dung hội thoại."
        }
      }
    ]
  }
}
```

---

## 4. ACTIVATION CODES MODULE

### 4.1 Code Generation (Admin Only)

#### POST /api/v1/admin/activation-codes/generate
**Mô tả**: Tạo mã kích hoạt (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Request Body:**
```javascript
{
  "type": "course", // course, subscription, bundle
  "grants": {
    "courseIds": ["course_id_1", "course_id_2"],
    "subscriptionPlan": "premium", // if type is subscription
    "subscriptionDuration": 365, // days
    "accessDuration": 180 // days, null for lifetime
  },
  "configuration": {
    "batchId": "BATCH_2024_001",
    "quantity": 100,
    "codePrefix": "ENG", // optional
    "codeLength": 12,
    "maxUses": 1,
    "validFrom": "2024-01-01T00:00:00.000Z",
    "validUntil": "2024-12-31T23:59:59.000Z",
    "region": "VN", // optional
    "userType": "any" // any, new_only, existing_only
  },
  "sales": {
    "distributorId": "distributor_user_id", // optional
    "originalPrice": 299000,
    "salePrice": 249000,
    "commission": 15 // percentage
  }
}
```

**Response (201 Created):**
```javascript
{
  "success": true,
  "data": {
    "batchId": "BATCH_2024_001",
    "totalGenerated": 100,
    "codes": [
      {
        "_id": "code_id",
        "code": "ENG123ABC456",
        "status": "active"
      }
    ],
    "downloadUrl": "https://api.lms.example.com/admin/activation-codes/download/BATCH_2024_001",
    "summary": {
      "type": "course",
      "totalValue": 29900000,
      "estimatedRevenue": 24900000,
      "validUntil": "2024-12-31T23:59:59.000Z"
    }
  },
  "message": "Đã tạo thành công 100 mã kích hoạt"
}
```

#### GET /api/v1/admin/activation-codes
**Mô tả**: Lấy danh sách mã kích hoạt (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Query Parameters:**
```javascript
{
  "page": 1,
  "limit": 50,
  "batchId": "BATCH_2024_001",
  "status": "active|used|expired|disabled",
  "type": "course|subscription|bundle",
  "distributorId": "distributor_id",
  "search": "search_term",
  "validFrom": "2024-01-01",
  "validUntil": "2024-12-31",
  "sortBy": "createdAt|usedAt|code",
  "sortOrder": "asc|desc"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": [
    {
      "_id": "code_id",
      "code": "ENG123ABC456",
      "type": "course",
      "status": "used",
      "grants": {
        "courseIds": ["course_id_1"],
        "courseNames": ["Tiếng Anh Giao Tiếp Cơ Bản"]
      },
      "configuration": {
        "batchId": "BATCH_2024_001",
        "maxUses": 1,
        "currentUses": 1,
        "validFrom": "2024-01-01T00:00:00.000Z",
        "validUntil": "2024-12-31T23:59:59.000Z"
      },
      "usage": [
        {
          "userId": "user_id",
          "userName": "Nguyễn Văn A",
          "usedAt": "2024-01-15T10:30:00.000Z"
        }
      ],
      "sales": {
        "distributorId": "distributor_id",
        "distributorName": "Đại lý ABC",
        "salePrice": 249000,
        "soldAt": "2024-01-10T00:00:00.000Z"
      },
      "createdAt": "2024-01-01T00:00:00.000Z"
    }
  ],
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 1000,
      "totalPages": 20
    },
    "summary": {
      "totalCodes": 1000,
      "activeCodes": 750,
      "usedCodes": 200,
      "expiredCodes": 50,
      "totalValue": 299000000,
      "usedValue": 59800000
    }
  }
}
```

#### GET /api/v1/admin/activation-codes/download/:batchId
**Mô tả**: Download danh sách mã kích hoạt (Excel/CSV)
**Authentication**: Required
**Authorization**: admin, super_admin

**Response**: File download (Excel/CSV)

### 4.2 Code Validation & Redemption

#### POST /api/v1/activation-codes/validate
**Mô tả**: Kiểm tra tính hợp lệ của mã kích hoạt
**Authentication**: Required

**Request Body:**
```javascript
{
  "code": "ENG123ABC456"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "valid": true,
    "code": "ENG123ABC456",
    "type": "course",
    "grants": {
      "courses": [
        {
          "_id": "course_id",
          "title": "Tiếng Anh Giao Tiếp Cơ Bản",
          "thumbnail": "https://cdn.example.com/courses/course1.jpg",
          "originalPrice": 299000
        }
      ],
      "accessDuration": 180, // days
      "totalValue": 299000
    },
    "restrictions": {
      "validUntil": "2024-12-31T23:59:59.000Z",
      "region": "VN",
      "userType": "any",
      "remainingUses": 1
    },
    "canRedeem": true,
    "redeemBy": "2024-12-31T23:59:59.000Z"
  },
  "message": "Mã kích hoạt hợp lệ"
}
```

#### POST /api/v1/activation-codes/redeem
**Mô tả**: Sử dụng mã kích hoạt
**Authentication**: Required

**Request Body:**
```javascript
{
  "code": "ENG123ABC456"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "redemption": {
      "_id": "redemption_id",
      "code": "ENG123ABC456",
      "redeemedAt": "2024-01-15T10:30:00.000Z"
    },
    "granted": {
      "courses": [
        {
          "_id": "course_id",
          "title": "Tiếng Anh Giao Tiếp Cơ Bản",
          "enrollmentId": "enrollment_id",
          "expiresAt": "2024-07-13T10:30:00.000Z"
        }
      ],
      "subscription": null
    },
    "totalValue": 299000
  },
  "message": "Kích hoạt thành công! Bạn đã được cấp quyền truy cập khóa học."
}
```

### 4.3 Code Management

#### PUT /api/v1/admin/activation-codes/:codeId
**Mô tả**: Cập nhật trạng thái mã kích hoạt (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Request Body:**
```javascript
{
  "status": "active|disabled",
  "validUntil": "2024-12-31T23:59:59.000Z",
  "maxUses": 2,
  "notes": "Ghi chú của admin"
}
```

#### GET /api/v1/admin/activation-codes/analytics
**Mô tả**: Thống kê mã kích hoạt (Admin)
**Authentication**: Required
**Authorization**: admin, super_admin

**Query Parameters:**
```javascript
{
  "period": "7d|30d|90d|1y",
  "batchId": "BATCH_2024_001",
  "distributorId": "distributor_id",
  "groupBy": "day|week|month"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "overview": {
      "totalCodes": 1000,
      "usedCodes": 200,
      "usageRate": 20,
      "totalRevenue": 59800000,
      "averageRedemptionTime": 5.2 // days
    },
    "trends": [
      {
        "date": "2024-01-15",
        "generated": 100,
        "used": 15,
        "revenue": 3735000
      }
    ],
    "topDistributors": [
      {
        "distributorId": "distributor_id",
        "distributorName": "Đại lý ABC",
        "codesUsed": 45,
        "revenue": 11205000,
        "commission": 1680750
      }
    ],
    "coursePerformance": [
      {
        "courseId": "course_id",
        "courseName": "Tiếng Anh Giao Tiếp Cơ Bản",
        "codesUsed": 120,
        "conversionRate": 85
      }
    ]
  }
}
```

---

## 5. ANALYTICS MODULE

### 5.1 Event Tracking

#### POST /api/v1/analytics/events
**Mô tả**: Ghi nhận sự kiện analytics
**Authentication**: Optional (for anonymous events)

**Request Body:**
```javascript
{
  "events": [
    {
      "name": "page_view",
      "category": "user",
      "action": "view",
      "label": "course_detail",
      "value": 1,
      "properties": {
        "courseId": "course_id",
        "courseName": "Tiếng Anh Giao Tiếp Cơ Bản",
        "source": "search",
        "medium": "organic"
      },
      "page": {
        "url": "/courses/tieng-anh-giao-tiep-co-ban",
        "title": "Tiếng Anh Giao Tiếp Cơ Bản",
        "referrer": "https://google.com"
      },
      "device": {
        "type": "desktop",
        "browser": "Chrome",
        "os": "Windows"
      }
    }
  ],
  "sessionId": "session_uuid",
  "userId": "user_id" // optional
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "eventsProcessed": 1,
    "sessionId": "session_uuid"
  }
}
```

#### POST /api/v1/analytics/events/batch
**Mô tả**: Ghi nhận nhiều sự kiện cùng lúc
**Authentication**: Optional

**Request Body:**
```javascript
{
  "events": [
    {
      "name": "lesson_start",
      "category": "course",
      "properties": {
        "courseId": "course_id",
        "lessonId": "lesson_id"
      },
      "timestamp": "2024-01-15T10:00:00.000Z"
    },
    {
      "name": "lesson_complete",
      "category": "course",
      "properties": {
        "courseId": "course_id",
        "lessonId": "lesson_id",
        "timeSpent": 900,
        "score": 85
      },
      "timestamp": "2024-01-15T10:15:00.000Z"
    }
  ]
}
```

### 5.2 Dashboard Analytics

#### GET /api/v1/analytics/dashboard
**Mô tả**: Lấy dữ liệu dashboard tổng quan
**Authentication**: Required
**Authorization**: admin, super_admin

**Query Parameters:**
```javascript
{
  "period": "7d|30d|90d|1y",
  "timezone": "Asia/Ho_Chi_Minh"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "overview": {
      "totalUsers": 15420,
      "activeUsers": 8750,
      "totalCourses": 45,
      "totalEnrollments": 23680,
      "totalRevenue": 2847500000,
      "conversionRate": 12.5
    },
    "trends": {
      "users": [
        {
          "date": "2024-01-15",
          "newUsers": 125,
          "activeUsers": 890,
          "retention": 78.5
        }
      ],
      "revenue": [
        {
          "date": "2024-01-15",
          "revenue": 15750000,
          "orders": 63,
          "averageOrderValue": 250000
        }
      ],
      "engagement": [
        {
          "date": "2024-01-15",
          "sessionsPerUser": 2.3,
          "averageSessionDuration": 1850,
          "bounceRate": 35.2
        }
      ]
    },
    "topCourses": [
      {
        "courseId": "course_id",
        "courseName": "Tiếng Anh Giao Tiếp Cơ Bản",
        "enrollments": 1250,
        "revenue": 373750000,
        "rating": 4.7,
        "completionRate": 78
      }
    ],
    "userSegments": {
      "byRole": {
        "student": 14500,
        "teacher": 850,
        "admin": 70
      },
      "bySubscription": {
        "free": 8900,
        "basic": 4200,
        "premium": 2320
      },
      "byActivity": {
        "active": 8750,
        "inactive": 4200,
        "churned": 2470
      }
    }
  }
}
```

#### GET /api/v1/analytics/courses/:courseId
**Mô tả**: Analytics chi tiết cho một khóa học
**Authentication**: Required
**Authorization**: Course instructor or admin

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "overview": {
      "totalEnrollments": 1250,
      "activeStudents": 890,
      "completionRate": 78,
      "averageRating": 4.7,
      "totalRevenue": 373750000,
      "averageProgress": 65.5
    },
    "enrollmentTrends": [
      {
        "date": "2024-01-15",
        "enrollments": 25,
        "completions": 18,
        "dropouts": 3
      }
    ],
    "lessonAnalytics": [
      {
        "lessonId": "lesson_id",
        "lessonName": "Bài 1: Chào hỏi cơ bản",
        "completionRate": 95,
        "averageTimeSpent": 850,
        "averageScore": 87,
        "dropoffRate": 5
      }
    ],
    "studentPerformance": {
      "averageScore": 82.5,
      "skillBreakdown": {
        "listening": 85,
        "speaking": 78,
        "reading": 88,
        "writing": 79
      },
      "commonMistakes": [
        "Pronunciation of 'th' sounds",
        "Present perfect tense usage"
      ]
    },
    "engagement": {
      "averageSessionDuration": 2150,
      "sessionsPerStudent": 3.2,
      "forumPosts": 245,
      "questionsAsked": 89
    }
  }
}
```

### 5.3 User Analytics

#### GET /api/v1/analytics/users/:userId
**Mô tả**: Analytics chi tiết cho một người dùng
**Authentication**: Required
**Authorization**: User themselves or admin

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "profile": {
      "userId": "user_id",
      "joinedAt": "2024-01-01T00:00:00.000Z",
      "lastActive": "2024-01-15T14:30:00.000Z",
      "totalSessions": 45,
      "totalTimeSpent": 67800 // seconds
    },
    "learning": {
      "coursesEnrolled": 5,
      "coursesCompleted": 2,
      "lessonsCompleted": 89,
      "averageScore": 85.5,
      "totalPoints": 1250,
      "currentStreak": 7,
      "longestStreak": 21
    },
    "skillProgress": {
      "listening": {
        "currentLevel": "intermediate",
        "progress": 75,
        "averageScore": 82,
        "improvement": 15 // percentage improvement
      },
      "speaking": {
        "currentLevel": "beginner",
        "progress": 45,
        "averageScore": 78,
        "improvement": 22
      },
      "reading": {
        "currentLevel": "intermediate",
        "progress": 80,
        "averageScore": 88,
        "improvement": 18
      },
      "writing": {
        "currentLevel": "beginner",
        "progress": 35,
        "averageScore": 75,
        "improvement": 12
      }
    },
    "activityTimeline": [
      {
        "date": "2024-01-15",
        "activities": [
          {
            "type": "lesson_complete",
            "courseId": "course_id",
            "lessonId": "lesson_id",
            "score": 90,
            "timestamp": "2024-01-15T10:30:00.000Z"
          }
        ],
        "timeSpent": 3600,
        "pointsEarned": 25
      }
    ],
    "achievements": [
      {
        "badgeId": "first_course_complete",
        "name": "Người hoàn thành đầu tiên",
        "earnedAt": "2024-01-10T15:00:00.000Z"
      }
    ]
  }
}
```

---

## 6. WEBSOCKET ENDPOINTS

### 6.1 Real-time Features

#### WebSocket Connection
**URL**: `wss://api.lms.example.com/ws`
**Authentication**: JWT token via query parameter or header

**Connection:**
```javascript
const ws = new WebSocket('wss://api.lms.example.com/ws?token=jwt_token');

// Or via header
const ws = new WebSocket('wss://api.lms.example.com/ws', {
  headers: {
    'Authorization': 'Bearer jwt_token'
  }
});
```

#### Message Format
```javascript
{
  "type": "message_type",
  "data": object,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "id": "message_uuid"
}
```

#### Supported Events

**1. Assessment Progress**
```javascript
// Client -> Server: Join assessment room
{
  "type": "join_assessment",
  "data": {
    "assessmentId": "assessment_id",
    "sessionId": "session_uuid"
  }
}

// Server -> Client: Time remaining update
{
  "type": "time_update",
  "data": {
    "timeRemaining": 1750, // seconds
    "warningAt": 300 // warn when 5 minutes left
  }
}

// Server -> Client: Auto-submit warning
{
  "type": "auto_submit_warning",
  "data": {
    "timeRemaining": 60,
    "message": "Bài kiểm tra sẽ tự động nộp sau 1 phút"
  }
}
```

**2. Live Classes**
```javascript
// Join live class
{
  "type": "join_class",
  "data": {
    "classId": "class_id",
    "role": "student|teacher"
  }
}

// Raise hand
{
  "type": "raise_hand",
  "data": {
    "userId": "user_id",
    "userName": "Nguyễn Văn A"
  }
}

// Chat message
{
  "type": "chat_message",
  "data": {
    "userId": "user_id",
    "userName": "Nguyễn Văn A",
    "message": "Tôi có câu hỏi về bài học",
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

**3. Notifications**
```javascript
// Real-time notification
{
  "type": "notification",
  "data": {
    "id": "notification_id",
    "title": "Bài kiểm tra mới",
    "message": "Bạn có bài kiểm tra mới trong khóa học Tiếng Anh",
    "type": "assessment",
    "priority": "medium",
    "actionUrl": "/assessments/assessment_id"
  }
}
```

---

## 7. WEBHOOK ENDPOINTS

### 7.1 Payment Webhooks

#### POST /api/v1/webhooks/stripe
**Mô tả**: Webhook từ Stripe
**Authentication**: Stripe signature verification

**Request Headers:**
```javascript
{
  "Stripe-Signature": "stripe_signature"
}
```

**Request Body (Payment Success):**
```javascript
{
  "type": "payment_intent.succeeded",
  "data": {
    "object": {
      "id": "pi_stripe_payment_id",
      "amount": 29900,
      "currency": "vnd",
      "metadata": {
        "userId": "user_id",
        "courseId": "course_id",
        "orderId": "order_id"
      }
    }
  }
}
```

#### POST /api/v1/webhooks/paypal
**Mô tả**: Webhook từ PayPal
**Authentication**: PayPal signature verification

### 7.2 AI Processing Webhooks

#### POST /api/v1/webhooks/ai/scoring-complete
**Mô tả**: Webhook khi AI hoàn thành chấm điểm
**Authentication**: Internal API key

**Request Body:**
```javascript
{
  "resultId": "result_id",
  "assessmentId": "assessment_id",
  "userId": "user_id",
  "status": "completed",
  "scores": {
    "overall": 85,
    "breakdown": {
      "pronunciation": 88,
      "fluency": 82,
      "grammar": 87,
      "vocabulary": 89
    }
  },
  "processingTime": 3.2,
  "confidence": 0.92
}
```

---

## 8. FILE UPLOAD ENDPOINTS

### 8.1 Course Content Upload

#### POST /api/v1/upload/course-video
**Mô tả**: Upload video bài học
**Authentication**: Required
**Authorization**: Course instructor or admin
**Content-Type**: multipart/form-data
**Max Size**: 500MB

**Request Body:**
```javascript
FormData {
  "video": File, // MP4, AVI, MOV
  "courseId": "course_id",
  "lessonId": "lesson_id",
  "generateSubtitles": true,
  "quality": "720p|1080p|auto"
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "videoId": "video_uuid",
    "originalUrl": "https://cdn.example.com/videos/original/video.mp4",
    "processedUrls": {
      "720p": "https://cdn.example.com/videos/720p/video.mp4",
      "480p": "https://cdn.example.com/videos/480p/video.mp4"
    },
    "thumbnail": "https://cdn.example.com/thumbnails/video.jpg",
    "duration": 1850, // seconds
    "size": 125000000, // bytes
    "status": "processing", // processing, ready, failed
    "subtitles": {
      "vi": "https://cdn.example.com/subtitles/video_vi.vtt",
      "en": "https://cdn.example.com/subtitles/video_en.vtt"
    }
  },
  "message": "Video đang được xử lý. Bạn sẽ nhận được thông báo khi hoàn thành."
}
```

#### POST /api/v1/upload/course-audio
**Mô tả**: Upload audio bài học
**Content-Type**: multipart/form-data
**Max Size**: 100MB

#### POST /api/v1/upload/course-document
**Mô tả**: Upload tài liệu khóa học
**Content-Type**: multipart/form-data
**Max Size**: 50MB
**Supported Formats**: PDF, DOC, DOCX, PPT, PPTX

### 8.2 Assessment Media Upload

#### POST /api/v1/upload/assessment-audio
**Mô tả**: Upload audio cho câu hỏi nghe
**Authentication**: Required
**Authorization**: Teacher or admin

#### POST /api/v1/upload/speaking-response
**Mô tả**: Upload bài nói của học viên
**Authentication**: Required
**Content-Type**: multipart/form-data
**Max Size**: 20MB

**Request Body:**
```javascript
FormData {
  "audio": File, // MP3, WAV, M4A
  "assessmentId": "assessment_id",
  "questionId": "question_id",
  "sessionId": "session_uuid",
  "duration": 45 // seconds
}
```

**Response (200 OK):**
```javascript
{
  "success": true,
  "data": {
    "audioId": "audio_uuid",
    "audioUrl": "https://cdn.example.com/audio/responses/audio.mp3",
    "duration": 45,
    "size": 2500000,
    "transcriptionStatus": "processing", // processing, completed, failed
    "scoringStatus": "queued" // queued, processing, completed
  }
}
```

---

## 9. SECURITY & VALIDATION

### 9.1 Rate Limiting

**Per User Limits:**
- Authentication endpoints: 10 requests/minute
- File upload: 5 requests/minute
- General API: 1000 requests/hour
- Assessment submission: 100 requests/hour

**Per IP Limits:**
- Registration: 5 requests/minute
- Password reset: 3 requests/minute
- General API: 10000 requests/hour

### 9.2 Input Validation

**Email Validation:**
```javascript
{
  "pattern": "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$",
  "maxLength": 255
}
```

**Password Requirements:**
```javascript
{
  "minLength": 8,
  "maxLength": 128,
  "requireUppercase": true,
  "requireLowercase": true,
  "requireNumbers": true,
  "requireSymbols": true,
  "forbiddenPatterns": ["123456", "password", "qwerty"]
}
```

**File Upload Validation:**
```javascript
{
  "video": {
    "maxSize": 500000000, // 500MB
    "allowedTypes": ["video/mp4", "video/avi", "video/mov"],
    "maxDuration": 7200 // 2 hours
  },
  "audio": {
    "maxSize": 100000000, // 100MB
    "allowedTypes": ["audio/mp3", "audio/wav", "audio/m4a"],
    "maxDuration": 3600 // 1 hour
  },
  "image": {
    "maxSize": 10000000, // 10MB
    "allowedTypes": ["image/jpeg", "image/png", "image/gif"],
    "maxDimensions": {
      "width": 4000,
      "height": 4000
    }
  }
}
```

### 9.3 CORS Configuration

```javascript
{
  "origin": [
    "https://lms.example.com",
    "https://admin.lms.example.com",
    "http://localhost:3000" // development only
  ],
  "methods": ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"],
  "allowedHeaders": [
    "Authorization",
    "Content-Type",
    "X-API-Version",
    "X-Client-Version"
  ],
  "credentials": true,
  "maxAge": 86400 // 24 hours
}
```

### 9.4 API Key Management

**API Key Types:**
- **User API Key**: Cho third-party integrations
- **System API Key**: Cho internal services
- **Webhook API Key**: Cho webhook verification

**API Key Format:**
```javascript
{
  "keyId": "lms_live_sk_1234567890abcdef",
  "permissions": ["read:courses", "write:enrollments"],
  "rateLimit": 5000, // requests per hour
  "expiresAt": "2024-12-31T23:59:59.000Z"
}
```

---

## 10. ERROR CODES & MESSAGES

### 10.1 HTTP Status Codes

- **200 OK**: Thành công
- **201 Created**: Tạo mới thành công
- **400 Bad Request**: Dữ liệu đầu vào không hợp lệ
- **401 Unauthorized**: Chưa xác thực
- **403 Forbidden**: Không có quyền truy cập
- **404 Not Found**: Không tìm thấy tài nguyên
- **409 Conflict**: Xung đột dữ liệu
- **422 Unprocessable Entity**: Dữ liệu không thể xử lý
- **429 Too Many Requests**: Vượt quá giới hạn request
- **500 Internal Server Error**: Lỗi server

### 10.2 Custom Error Codes

```javascript
{
  // Authentication Errors
  "AUTH_INVALID_CREDENTIALS": "Email hoặc mật khẩu không chính xác",
  "AUTH_ACCOUNT_LOCKED": "Tài khoản đã bị khóa do đăng nhập sai quá nhiều lần",
  "AUTH_EMAIL_NOT_VERIFIED": "Vui lòng xác thực email trước khi đăng nhập",
  "AUTH_TOKEN_EXPIRED": "Token đã hết hạn, vui lòng đăng nhập lại",

  // Course Errors
  "COURSE_NOT_FOUND": "Không tìm thấy khóa học",
  "COURSE_NOT_PUBLISHED": "Khóa học chưa được xuất bản",
  "COURSE_ENROLLMENT_CLOSED": "Đã hết hạn đăng ký khóa học",
  "COURSE_ALREADY_ENROLLED": "Bạn đã đăng ký khóa học này rồi",

  // Assessment Errors
  "ASSESSMENT_NOT_AVAILABLE": "Bài kiểm tra không khả dụng",
  "ASSESSMENT_MAX_ATTEMPTS": "Bạn đã hết số lần làm bài",
  "ASSESSMENT_TIME_EXPIRED": "Đã hết thời gian làm bài",
  "ASSESSMENT_ALREADY_SUBMITTED": "Bài kiểm tra đã được nộp",

  // Activation Code Errors
  "CODE_INVALID": "Mã kích hoạt không hợp lệ",
  "CODE_EXPIRED": "Mã kích hoạt đã hết hạn",
  "CODE_ALREADY_USED": "Mã kích hoạt đã được sử dụng",
  "CODE_REGION_RESTRICTED": "Mã kích hoạt không khả dụng ở khu vực này",

  // File Upload Errors
  "FILE_TOO_LARGE": "File quá lớn",
  "FILE_TYPE_NOT_SUPPORTED": "Định dạng file không được hỗ trợ",
  "FILE_UPLOAD_FAILED": "Upload file thất bại",

  // Payment Errors
  "PAYMENT_FAILED": "Thanh toán thất bại",
  "PAYMENT_CANCELLED": "Thanh toán đã bị hủy",
  "PAYMENT_INSUFFICIENT_FUNDS": "Không đủ số dư",

  // General Errors
  "VALIDATION_ERROR": "Dữ liệu đầu vào không hợp lệ",
  "PERMISSION_DENIED": "Bạn không có quyền thực hiện hành động này",
  "RESOURCE_NOT_FOUND": "Không tìm thấy tài nguyên",
  "RATE_LIMIT_EXCEEDED": "Vượt quá giới hạn số lần gọi API"
}
```

---

## 11. API VERSIONING STRATEGY

### 11.1 Versioning Approach
- **URL Versioning**: `/api/v1/`, `/api/v2/`
- **Backward Compatibility**: Maintain v1 for 12 months after v2 release
- **Deprecation Notice**: 6 months advance notice via headers

### 11.2 Version Headers
```javascript
{
  "X-API-Version": "v1",
  "X-API-Deprecated": "false",
  "X-API-Sunset": "2025-01-01T00:00:00.000Z" // if deprecated
}
```

---

## 12. INTEGRATION EXAMPLES

### 12.1 Frontend Integration (React/Next.js)

```javascript
// API Client Setup
import axios from 'axios';

const apiClient = axios.create({
  baseURL: 'https://api.lms.example.com/api/v1',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Version': 'v1'
  }
});

// Request interceptor for auth
apiClient.interceptors.request.use((config) => {
  const token = localStorage.getItem('accessToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    if (error.response?.status === 401) {
      // Handle token refresh
      const refreshToken = localStorage.getItem('refreshToken');
      if (refreshToken) {
        try {
          const response = await apiClient.post('/auth/refresh', {
            refreshToken
          });
          const { accessToken } = response.data.data.tokens;
          localStorage.setItem('accessToken', accessToken);
          // Retry original request
          return apiClient.request(error.config);
        } catch (refreshError) {
          // Redirect to login
          window.location.href = '/login';
        }
      }
    }
    return Promise.reject(error);
  }
);

// Usage Examples
export const authAPI = {
  login: (credentials) => apiClient.post('/auth/login', credentials),
  register: (userData) => apiClient.post('/auth/register', userData),
  logout: () => apiClient.post('/auth/logout')
};

export const courseAPI = {
  getCourses: (params) => apiClient.get('/courses', { params }),
  getCourse: (id) => apiClient.get(`/courses/${id}`),
  enrollCourse: (id, data) => apiClient.post(`/courses/${id}/enroll`, data)
};
```

### 12.2 Mobile Integration (React Native)

```javascript
// File Upload with Progress
const uploadVideo = async (videoUri, courseId, lessonId) => {
  const formData = new FormData();
  formData.append('video', {
    uri: videoUri,
    type: 'video/mp4',
    name: 'lesson_video.mp4'
  });
  formData.append('courseId', courseId);
  formData.append('lessonId', lessonId);

  try {
    const response = await fetch('/api/v1/upload/course-video', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'multipart/form-data'
      },
      body: formData
    });

    return await response.json();
  } catch (error) {
    console.error('Upload failed:', error);
    throw error;
  }
};
```

---

## KẾT LUẬN

API Design Specification này cung cấp:

✅ **Comprehensive Coverage**: 200+ endpoints cho tất cả modules
✅ **Detailed Specifications**: Request/Response schemas chi tiết
✅ **Security First**: Authentication, authorization, validation
✅ **Real-time Features**: WebSocket và webhook support
✅ **Developer Friendly**: Clear documentation và examples
✅ **Scalable Architecture**: Rate limiting, caching, versioning
✅ **Error Handling**: Comprehensive error codes và messages

**Các bước triển khai tiếp theo:**
1. **Backend Implementation**: Implement API endpoints theo specification
2. **API Testing**: Tạo test suite cho tất cả endpoints
3. **Documentation**: Generate interactive API docs (Swagger/OpenAPI)
4. **SDK Development**: Tạo SDK cho JavaScript/TypeScript
5. **Monitoring Setup**: API monitoring và analytics
6. **Performance Testing**: Load testing và optimization
```
```
