# Code Standards & Guidelines - <PERSON><PERSON> thống LMS

## Tổng quan
Tài liệu này định nghĩa coding standards và clean code guidelines cho hệ thống LMS Next.js fullstack. Mục tiêu: code dễ đọc, dễ maintain, và consistent across team.

---

## 1. CẤU TRÚC VÀ TỔ CHỨC

### 1.1 Folder Structure Convention

```
src/
├── app/                          # Next.js 13+ App Router
│   ├── (auth)/                   # Route groups
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/
│   │   ├── courses/
│   │   ├── assessments/
│   │   └── analytics/
│   ├── api/                      # API routes
│   │   ├── auth/
│   │   ├── courses/
│   │   ├── assessments/
│   │   └── webhooks/
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/                   # Reusable components
│   ├── ui/                      # Basic UI components
│   │   ├── Button/
│   │   ├── Input/
│   │   └── Modal/
│   ├── forms/                   # Form components
│   ├── course/                  # Course-specific components
│   ├── assessment/              # Assessment components
│   └── layout/                  # Layout components
├── lib/                         # Utilities và configurations
│   ├── auth/                    # Authentication logic
│   ├── database/                # Database connections
│   ├── ai/                      # AI integration
│   ├── utils/                   # Helper functions
│   ├── validations/             # Zod schemas
│   └── constants/               # App constants
├── types/                       # TypeScript type definitions
│   ├── auth.ts
│   ├── course.ts
│   ├── assessment.ts
│   └── api.ts
├── hooks/                       # Custom React hooks
├── stores/                      # State management (Zustand)
└── middleware.ts                # Next.js middleware
```

### 1.2 File Naming Conventions

**✅ Đúng:**
```
// Components: PascalCase
CourseCard.tsx
AssessmentForm.tsx
UserProfile.tsx

// Pages: kebab-case
course-detail.tsx
assessment-results.tsx

// API routes: kebab-case
/api/courses/[courseId]/enroll.ts
/api/assessments/submit-answer.ts

// Utilities: camelCase
formatCurrency.ts
validateEmail.ts
aiScoringService.ts

// Types: PascalCase
CourseTypes.ts
AssessmentTypes.ts

// Constants: UPPER_SNAKE_CASE
API_ENDPOINTS.ts
ERROR_MESSAGES.ts
```

**❌ Sai:**
```
coursecard.tsx          // Should be CourseCard.tsx
Course_Detail.tsx       // Should be course-detail.tsx
api_endpoints.ts        // Should be API_ENDPOINTS.ts
```

### 1.3 Import/Export Organization

**✅ Import Order:**
```typescript
// 1. External libraries
import React from 'react'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 2. Internal utilities và configs
import { connectDB } from '@/lib/database'
import { authOptions } from '@/lib/auth'

// 3. Types
import type { Course, User } from '@/types'

// 4. Components
import { Button } from '@/components/ui/Button'
import { CourseCard } from '@/components/course/CourseCard'

// 5. Relative imports
import './styles.css'
```

**✅ Export Patterns:**
```typescript
// Named exports cho utilities
export const formatCurrency = (amount: number) => { ... }
export const validateEmail = (email: string) => { ... }

// Default export cho components
export default function CourseCard({ course }: CourseCardProps) { ... }

// Re-exports cho barrel files
export { Button } from './Button'
export { Input } from './Input'
export { Modal } from './Modal'
```

---

## 2. CODING STANDARDS

### 2.1 TypeScript Best Practices

**✅ Type Definitions:**
```typescript
// Sử dụng interface cho object shapes
interface User {
  readonly id: string
  email: string
  profile: UserProfile
  role: UserRole
  createdAt: Date
  updatedAt: Date
}

// Sử dụng type cho unions và computed types
type UserRole = 'student' | 'teacher' | 'admin' | 'super_admin'
type CourseStatus = 'draft' | 'published' | 'archived'

// Generic types cho reusable patterns
interface ApiResponse<T> {
  success: boolean
  data: T | null
  message: string
  errors?: ValidationError[]
}

// Utility types
type CreateUserRequest = Omit<User, 'id' | 'createdAt' | 'updatedAt'>
type UpdateUserRequest = Partial<Pick<User, 'email' | 'profile'>>
```

**✅ Function Signatures:**
```typescript
// Explicit return types cho public functions
export async function createCourse(
  data: CreateCourseRequest
): Promise<ApiResponse<Course>> {
  // Implementation
}

// Generic functions
export function createApiResponse<T>(
  data: T,
  message: string = 'Success'
): ApiResponse<T> {
  return {
    success: true,
    data,
    message,
    errors: []
  }
}
```

### 2.2 React/Next.js Component Patterns

**✅ Functional Components với TypeScript:**
```typescript
interface CourseCardProps {
  course: Course
  onEnroll?: (courseId: string) => void
  showPrice?: boolean
  className?: string
}

export default function CourseCard({
  course,
  onEnroll,
  showPrice = true,
  className = ''
}: CourseCardProps) {
  // Hooks ở đầu component
  const [isLoading, setIsLoading] = useState(false)
  const { user } = useAuth()
  
  // Event handlers
  const handleEnroll = useCallback(async () => {
    if (!onEnroll) return
    
    setIsLoading(true)
    try {
      await onEnroll(course.id)
    } catch (error) {
      console.error('Enrollment failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [course.id, onEnroll])
  
  // Early returns
  if (!course) {
    return <div>Course not found</div>
  }
  
  // Main render
  return (
    <div className={`course-card ${className}`}>
      {/* Component content */}
    </div>
  )
}
```

**✅ Custom Hooks Pattern:**
```typescript
// hooks/useCourseEnrollment.ts
interface UseCourseEnrollmentReturn {
  enrollCourse: (courseId: string) => Promise<void>
  isEnrolling: boolean
  error: string | null
}

export function useCourseEnrollment(): UseCourseEnrollmentReturn {
  const [isEnrolling, setIsEnrolling] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  const enrollCourse = useCallback(async (courseId: string) => {
    setIsEnrolling(true)
    setError(null)
    
    try {
      const response = await fetch(`/api/courses/${courseId}/enroll`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      })
      
      if (!response.ok) {
        throw new Error('Enrollment failed')
      }
      
      // Thành công - có thể trigger revalidation
      window.location.reload()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setIsEnrolling(false)
    }
  }, [])
  
  return { enrollCourse, isEnrolling, error }
}
```

### 2.3 API Routes Structure

**✅ API Route Pattern:**
```typescript
// app/api/courses/[courseId]/enroll/route.ts
import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { z } from 'zod'

import { connectDB } from '@/lib/database'
import { authOptions } from '@/lib/auth'
import { createApiResponse, createErrorResponse } from '@/lib/utils/api'
import type { ApiResponse, EnrollmentData } from '@/types'

// Validation schema
const enrollRequestSchema = z.object({
  paymentMethod: z.enum(['stripe', 'paypal', 'activation_code']),
  activationCode: z.string().optional(),
  couponCode: z.string().optional()
})

export async function POST(
  request: NextRequest,
  { params }: { params: { courseId: string } }
): Promise<NextResponse<ApiResponse<EnrollmentData>>> {
  try {
    // 1. Authentication check
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return createErrorResponse('Unauthorized', 401)
    }
    
    // 2. Input validation
    const body = await request.json()
    const validatedData = enrollRequestSchema.parse(body)
    
    // 3. Database connection
    await connectDB()
    
    // 4. Business logic
    const enrollmentResult = await enrollUserInCourse({
      userId: session.user.id,
      courseId: params.courseId,
      ...validatedData
    })
    
    // 5. Success response
    return NextResponse.json(
      createApiResponse(enrollmentResult, 'Đăng ký khóa học thành công'),
      { status: 201 }
    )
    
  } catch (error) {
    // 6. Error handling
    console.error('Course enrollment error:', error)
    
    if (error instanceof z.ZodError) {
      return createErrorResponse('Invalid input data', 400, error.errors)
    }
    
    return createErrorResponse('Internal server error', 500)
  }
}
```

### 2.4 Database Query Patterns

**✅ Mongoose Model Pattern:**
```typescript
// lib/database/models/Course.ts
import mongoose, { Schema, Document } from 'mongoose'

interface ICourse extends Document {
  title: string
  slug: string
  description: string
  instructor: mongoose.Types.ObjectId
  pricing: {
    type: 'free' | 'paid'
    price: number
    currency: string
  }
  stats: {
    totalEnrollments: number
    averageRating: number
  }
  createdAt: Date
  updatedAt: Date
}

const courseSchema = new Schema<ICourse>({
  title: { type: String, required: true, trim: true },
  slug: { type: String, required: true, unique: true },
  description: { type: String, required: true },
  instructor: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  pricing: {
    type: { type: String, enum: ['free', 'paid'], default: 'free' },
    price: { type: Number, default: 0 },
    currency: { type: String, default: 'VND' }
  },
  stats: {
    totalEnrollments: { type: Number, default: 0 },
    averageRating: { type: Number, default: 0 }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
})

// Indexes cho performance
courseSchema.index({ slug: 1 })
courseSchema.index({ instructor: 1, createdAt: -1 })
courseSchema.index({ 'pricing.type': 1, 'pricing.price': 1 })

// Virtual fields
courseSchema.virtual('enrollments', {
  ref: 'Enrollment',
  localField: '_id',
  foreignField: 'courseId'
})

export const Course = mongoose.models.Course || mongoose.model<ICourse>('Course', courseSchema)
```

**✅ Database Service Pattern:**
```typescript
// lib/database/services/courseService.ts
import { Course } from '../models/Course'
import { Enrollment } from '../models/Enrollment'
import type { CreateCourseData, CourseFilters } from '@/types'

export class CourseService {
  // Tạo khóa học mới
  static async createCourse(data: CreateCourseData): Promise<ICourse> {
    try {
      const course = new Course(data)
      await course.save()
      return course
    } catch (error) {
      console.error('Error creating course:', error)
      throw new Error('Không thể tạo khóa học')
    }
  }
  
  // Lấy danh sách khóa học với filters và pagination
  static async getCourses(filters: CourseFilters = {}) {
    const {
      page = 1,
      limit = 12,
      category,
      level,
      priceType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = filters
    
    // Build query
    const query: any = {}
    
    if (category) query.category = category
    if (level) query.level = level
    if (priceType) query['pricing.type'] = priceType
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } }
      ]
    }
    
    // Execute query với pagination
    const skip = (page - 1) * limit
    const sortOptions: any = {}
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1
    
    const [courses, total] = await Promise.all([
      Course.find(query)
        .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .lean(),
      Course.countDocuments(query)
    ])
    
    return {
      courses,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    }
  }
  
  // Đăng ký khóa học
  static async enrollUserInCourse(userId: string, courseId: string) {
    const session = await mongoose.startSession()
    
    try {
      session.startTransaction()
      
      // Kiểm tra đã đăng ký chưa
      const existingEnrollment = await Enrollment.findOne({
        userId,
        courseId
      }).session(session)
      
      if (existingEnrollment) {
        throw new Error('Bạn đã đăng ký khóa học này rồi')
      }
      
      // Tạo enrollment mới
      const enrollment = new Enrollment({
        userId,
        courseId,
        enrolledAt: new Date(),
        status: 'enrolled'
      })
      
      await enrollment.save({ session })
      
      // Cập nhật stats của course
      await Course.findByIdAndUpdate(
        courseId,
        { $inc: { 'stats.totalEnrollments': 1 } },
        { session }
      )
      
      await session.commitTransaction()
      return enrollment
      
    } catch (error) {
      await session.abortTransaction()
      throw error
    } finally {
      session.endSession()
    }
  }
}
```

---

## 3. CODE QUALITY RULES

### 3.1 Naming Conventions

**✅ Variables và Functions:**
```typescript
// Variables: camelCase, descriptive
const userEnrollmentData = await fetchUserEnrollments()
const isAssessmentCompleted = checkAssessmentStatus()
const totalCourseRevenue = calculateRevenue()

// Functions: verb + noun, camelCase
function calculateAssessmentScore(responses: AssessmentResponse[]): number { }
function validateUserPermissions(user: User, resource: string): boolean { }
function formatCurrencyDisplay(amount: number, currency: string): string { }

// Boolean variables: is/has/can/should prefix
const isUserAuthenticated = !!session
const hasActiveSubscription = user.subscription?.status === 'active'
const canAccessCourse = checkCourseAccess(user, course)
const shouldShowUpgrade = !user.isPremium

// Constants: UPPER_SNAKE_CASE
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB
const DEFAULT_PAGE_SIZE = 20
const AI_SCORING_TIMEOUT = 30000 // 30 seconds
```

**❌ Tránh:**
```typescript
// Tên không rõ nghĩa
const data = await fetch() // Should be: courseData, userData, etc.
const result = calculate() // Should be: assessmentScore, totalRevenue, etc.

// Tên quá ngắn
const u = getCurrentUser() // Should be: user, currentUser
const c = getCourse() // Should be: course, courseData

// Tên quá dài
const userEnrollmentDataWithCourseInformationAndProgress = {} // Too verbose
```

### 3.2 Comment Guidelines

**✅ Business Logic Comments (Tiếng Việt):**
```typescript
export async function calculateAssessmentScore(responses: AssessmentResponse[]) {
  // Tính điểm tổng dựa trên trọng số của từng kỹ năng
  // Listening: 25%, Speaking: 30%, Reading: 25%, Writing: 20%
  const skillWeights = {
    listening: 0.25,
    speaking: 0.30,
    reading: 0.25,
    writing: 0.20
  }
  
  // Nhóm câu trả lời theo kỹ năng
  const responsesBySkill = groupResponsesBySkill(responses)
  
  let totalScore = 0
  
  for (const [skill, skillResponses] of Object.entries(responsesBySkill)) {
    // Tính điểm trung bình cho từng kỹ năng
    const skillAverage = calculateSkillAverage(skillResponses)
    
    // Áp dụng trọng số
    totalScore += skillAverage * skillWeights[skill as keyof typeof skillWeights]
  }
  
  // Làm tròn đến 1 chữ số thập phân
  return Math.round(totalScore * 10) / 10
}
```

**✅ Technical Comments (Tiếng Anh):**
```typescript
// Cache user permissions for 5 minutes to reduce database calls
const userPermissions = await redis.get(`permissions:${userId}`, {
  ttl: 300
})

// Use transaction to ensure data consistency
const session = await mongoose.startSession()
session.startTransaction()

// Debounce search input to avoid excessive API calls
const debouncedSearch = useMemo(
  () => debounce(handleSearch, 300),
  [handleSearch]
)
```

**✅ JSDoc cho Public APIs:**
```typescript
/**
 * Chấm điểm bài nói tự động bằng AI
 * 
 * @param audioUrl - URL của file audio cần chấm điểm
 * @param questionText - Nội dung câu hỏi
 * @param criteria - Các tiêu chí chấm điểm
 * @returns Promise với kết quả chấm điểm chi tiết
 * 
 * @example
 * ```typescript
 * const result = await scoreSpeakingAssessment(
 *   'https://cdn.example.com/audio.mp3',
 *   'Describe your daily routine',
 *   ['pronunciation', 'fluency', 'grammar']
 * )
 * ```
 */
export async function scoreSpeakingAssessment(
  audioUrl: string,
  questionText: string,
  criteria: ScoringCriteria[]
): Promise<SpeakingAssessmentResult> {
  // Implementation
}
```

### 3.3 Error Handling Standards

**✅ Error Handling Pattern:**
```typescript
// Custom error classes
export class CourseNotFoundError extends Error {
  constructor(courseId: string) {
    super(`Course with ID ${courseId} not found`)
    this.name = 'CourseNotFoundError'
  }
}

export class InsufficientPermissionsError extends Error {
  constructor(action: string) {
    super(`Insufficient permissions to ${action}`)
    this.name = 'InsufficientPermissionsError'
  }
}

// Error handling trong API routes
export async function POST(request: NextRequest) {
  try {
    // Business logic
    const result = await processEnrollment(data)
    return NextResponse.json(createApiResponse(result))
    
  } catch (error) {
    // Log error với context
    console.error('Enrollment processing failed:', {
      error: error.message,
      stack: error.stack,
      userId: session?.user?.id,
      courseId: data.courseId,
      timestamp: new Date().toISOString()
    })
    
    // Return appropriate error response
    if (error instanceof CourseNotFoundError) {
      return createErrorResponse('Không tìm thấy khóa học', 404)
    }
    
    if (error instanceof InsufficientPermissionsError) {
      return createErrorResponse('Không có quyền truy cập', 403)
    }
    
    // Generic error
    return createErrorResponse('Đã xảy ra lỗi, vui lòng thử lại', 500)
  }
}

// Error boundary cho React components
export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false, error: null }
  }
  
  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }
  
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error to monitoring service
    console.error('React Error Boundary caught an error:', {
      error: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString()
    })
  }
  
  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Đã xảy ra lỗi</h2>
          <p>Vui lòng tải lại trang hoặc liên hệ hỗ trợ.</p>
          <button onClick={() => window.location.reload()}>
            Tải lại trang
          </button>
        </div>
      )
    }
    
    return this.props.children
  }
}
```

### 3.4 Logging Conventions

**✅ Structured Logging:**
```typescript
// lib/utils/logger.ts
interface LogContext {
  userId?: string
  sessionId?: string
  requestId?: string
  action?: string
  resource?: string
  metadata?: Record<string, any>
}

export class Logger {
  static info(message: string, context?: LogContext) {
    console.log(JSON.stringify({
      level: 'info',
      message,
      timestamp: new Date().toISOString(),
      ...context
    }))
  }

  static error(message: string, error?: Error, context?: LogContext) {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: error?.message,
      stack: error?.stack,
      timestamp: new Date().toISOString(),
      ...context
    }))
  }

  static warn(message: string, context?: LogContext) {
    console.warn(JSON.stringify({
      level: 'warn',
      message,
      timestamp: new Date().toISOString(),
      ...context
    }))
  }
}

// Usage examples
Logger.info('User enrolled in course', {
  userId: user.id,
  action: 'course_enrollment',
  resource: `course:${courseId}`,
  metadata: { paymentMethod: 'stripe' }
})

Logger.error('AI scoring failed', error, {
  userId: user.id,
  action: 'ai_scoring',
  resource: `assessment:${assessmentId}`,
  metadata: { audioUrl, duration }
})
```

### 3.5 Performance Optimization Guidelines

**✅ React Performance:**
```typescript
// Memoization cho expensive calculations
const expensiveValue = useMemo(() => {
  return calculateComplexScore(assessmentData)
}, [assessmentData])

// Callback memoization
const handleCourseEnroll = useCallback(async (courseId: string) => {
  await enrollInCourse(courseId)
}, [enrollInCourse])

// Component memoization
const CourseCard = memo(function CourseCard({ course, onEnroll }: Props) {
  return <div>{/* Component content */}</div>
})

// Lazy loading cho large components
const AssessmentEditor = lazy(() => import('./AssessmentEditor'))
const VideoPlayer = lazy(() => import('./VideoPlayer'))

function CourseContent() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <VideoPlayer src={videoUrl} />
    </Suspense>
  )
}
```

**✅ Database Performance:**
```typescript
// Sử dụng lean() cho read-only queries
const courses = await Course.find(query).lean()

// Populate chỉ fields cần thiết
const courseWithInstructor = await Course.findById(courseId)
  .populate('instructor', 'profile.firstName profile.lastName profile.avatar')
  .lean()

// Batch operations
const bulkOps = enrollments.map(enrollment => ({
  updateOne: {
    filter: { _id: enrollment._id },
    update: { $set: { status: 'completed' } }
  }
}))
await Enrollment.bulkWrite(bulkOps)

// Aggregation pipeline cho complex queries
const courseStats = await Course.aggregate([
  { $match: { instructor: instructorId } },
  { $lookup: {
      from: 'enrollments',
      localField: '_id',
      foreignField: 'courseId',
      as: 'enrollments'
  }},
  { $project: {
      title: 1,
      totalEnrollments: { $size: '$enrollments' },
      completedEnrollments: {
        $size: {
          $filter: {
            input: '$enrollments',
            cond: { $eq: ['$$this.status', 'completed'] }
          }
        }
      }
  }}
])
```

---

## 4. SPECIFIC GUIDELINES CHO LMS

### 4.1 AI Integration Code Patterns

**✅ AI Service Pattern:**
```typescript
// lib/ai/openaiService.ts
import OpenAI from 'openai'

interface SpeakingAssessmentRequest {
  audioUrl: string
  questionText: string
  level: 'beginner' | 'intermediate' | 'advanced'
  criteria: string[]
}

interface SpeakingAssessmentResult {
  overallScore: number
  criteriaScores: Record<string, number>
  feedback: string
  transcription: string
  confidence: number
}

export class OpenAIService {
  private client: OpenAI

  constructor() {
    this.client = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY
    })
  }

  async scoreSpeakingAssessment(
    request: SpeakingAssessmentRequest
  ): Promise<SpeakingAssessmentResult> {
    try {
      // 1. Transcribe audio
      const transcription = await this.transcribeAudio(request.audioUrl)

      // 2. Score based on criteria
      const scoringPrompt = this.buildScoringPrompt(
        transcription,
        request.questionText,
        request.level,
        request.criteria
      )

      const completion = await this.client.chat.completions.create({
        model: 'gpt-4',
        messages: [
          {
            role: 'system',
            content: 'You are an expert English language assessor...'
          },
          {
            role: 'user',
            content: scoringPrompt
          }
        ],
        temperature: 0.3, // Lower temperature for consistent scoring
        max_tokens: 1000
      })

      // 3. Parse response
      const result = this.parseAssessmentResult(completion.choices[0].message.content)

      // 4. Log for monitoring
      Logger.info('AI assessment completed', {
        action: 'ai_speaking_assessment',
        metadata: {
          level: request.level,
          criteria: request.criteria,
          overallScore: result.overallScore,
          confidence: result.confidence
        }
      })

      return {
        ...result,
        transcription
      }

    } catch (error) {
      Logger.error('AI assessment failed', error, {
        action: 'ai_speaking_assessment',
        metadata: request
      })
      throw new Error('Không thể chấm điểm bài nói, vui lòng thử lại')
    }
  }

  private buildScoringPrompt(
    transcription: string,
    questionText: string,
    level: string,
    criteria: string[]
  ): string {
    return `
Đánh giá bài nói tiếng Anh sau đây:

Câu hỏi: ${questionText}
Cấp độ: ${level}
Bài trả lời: ${transcription}

Tiêu chí đánh giá: ${criteria.join(', ')}

Vui lòng đánh giá theo format JSON:
{
  "overallScore": number (0-10),
  "criteriaScores": {
    "pronunciation": number (0-10),
    "fluency": number (0-10),
    "grammar": number (0-10),
    "vocabulary": number (0-10)
  },
  "feedback": "string",
  "confidence": number (0-1)
}
    `.trim()
  }
}
```

### 4.2 File Upload Handling Standards

**✅ File Upload Service:**
```typescript
// lib/upload/fileUploadService.ts
import { v4 as uuidv4 } from 'uuid'
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3'

interface UploadConfig {
  maxSize: number
  allowedTypes: string[]
  folder: string
}

interface UploadResult {
  fileId: string
  url: string
  originalName: string
  size: number
  type: string
}

export class FileUploadService {
  private s3Client: S3Client

  constructor() {
    this.s3Client = new S3Client({
      region: process.env.AWS_REGION,
      credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!
      }
    })
  }

  async uploadFile(
    file: File,
    config: UploadConfig,
    userId: string
  ): Promise<UploadResult> {
    // 1. Validate file
    this.validateFile(file, config)

    // 2. Generate unique filename
    const fileId = uuidv4()
    const extension = file.name.split('.').pop()
    const fileName = `${fileId}.${extension}`
    const key = `${config.folder}/${userId}/${fileName}`

    try {
      // 3. Upload to S3
      const buffer = await file.arrayBuffer()

      await this.s3Client.send(new PutObjectCommand({
        Bucket: process.env.AWS_S3_BUCKET,
        Key: key,
        Body: new Uint8Array(buffer),
        ContentType: file.type,
        Metadata: {
          originalName: file.name,
          uploadedBy: userId,
          uploadedAt: new Date().toISOString()
        }
      }))

      // 4. Generate public URL
      const url = `https://${process.env.AWS_S3_BUCKET}.s3.${process.env.AWS_REGION}.amazonaws.com/${key}`

      // 5. Log upload
      Logger.info('File uploaded successfully', {
        userId,
        action: 'file_upload',
        metadata: {
          fileId,
          originalName: file.name,
          size: file.size,
          type: file.type,
          folder: config.folder
        }
      })

      return {
        fileId,
        url,
        originalName: file.name,
        size: file.size,
        type: file.type
      }

    } catch (error) {
      Logger.error('File upload failed', error, {
        userId,
        action: 'file_upload',
        metadata: { fileName: file.name, size: file.size }
      })
      throw new Error('Không thể upload file, vui lòng thử lại')
    }
  }

  private validateFile(file: File, config: UploadConfig): void {
    // Check file size
    if (file.size > config.maxSize) {
      throw new Error(`File quá lớn. Kích thước tối đa: ${config.maxSize / 1024 / 1024}MB`)
    }

    // Check file type
    if (!config.allowedTypes.includes(file.type)) {
      throw new Error(`Định dạng file không được hỗ trợ. Cho phép: ${config.allowedTypes.join(', ')}`)
    }

    // Check file name
    if (file.name.length > 255) {
      throw new Error('Tên file quá dài')
    }
  }
}

// Upload configurations
export const UPLOAD_CONFIGS = {
  courseVideo: {
    maxSize: 500 * 1024 * 1024, // 500MB
    allowedTypes: ['video/mp4', 'video/avi', 'video/mov'],
    folder: 'courses/videos'
  },
  assessmentAudio: {
    maxSize: 20 * 1024 * 1024, // 20MB
    allowedTypes: ['audio/mp3', 'audio/wav', 'audio/m4a'],
    folder: 'assessments/audio'
  },
  userAvatar: {
    maxSize: 5 * 1024 * 1024, // 5MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/gif'],
    folder: 'users/avatars'
  }
} as const
```

### 4.3 Real-time Features (WebSocket) Code Structure

**✅ WebSocket Service Pattern:**
```typescript
// lib/websocket/websocketService.ts
import { Server as SocketIOServer } from 'socket.io'
import { Server as HTTPServer } from 'http'
import jwt from 'jsonwebtoken'

interface SocketUser {
  id: string
  email: string
  role: string
}

interface AssessmentSession {
  sessionId: string
  userId: string
  assessmentId: string
  startTime: Date
  endTime: Date
}

export class WebSocketService {
  private io: SocketIOServer
  private assessmentSessions = new Map<string, AssessmentSession>()

  constructor(server: HTTPServer) {
    this.io = new SocketIOServer(server, {
      cors: {
        origin: process.env.FRONTEND_URL,
        methods: ['GET', 'POST']
      }
    })

    this.setupMiddleware()
    this.setupEventHandlers()
  }

  private setupMiddleware() {
    // Authentication middleware
    this.io.use(async (socket, next) => {
      try {
        const token = socket.handshake.auth.token || socket.handshake.headers.authorization?.split(' ')[1]

        if (!token) {
          throw new Error('No token provided')
        }

        const decoded = jwt.verify(token, process.env.JWT_SECRET!) as any
        const user: SocketUser = {
          id: decoded.userId,
          email: decoded.email,
          role: decoded.role
        }

        socket.data.user = user
        next()
      } catch (error) {
        Logger.error('WebSocket authentication failed', error)
        next(new Error('Authentication failed'))
      }
    })
  }

  private setupEventHandlers() {
    this.io.on('connection', (socket) => {
      const user = socket.data.user as SocketUser

      Logger.info('User connected to WebSocket', {
        userId: user.id,
        action: 'websocket_connect',
        metadata: { socketId: socket.id }
      })

      // Join user to their personal room
      socket.join(`user:${user.id}`)

      // Assessment events
      socket.on('join_assessment', this.handleJoinAssessment.bind(this, socket))
      socket.on('leave_assessment', this.handleLeaveAssessment.bind(this, socket))

      // Live class events
      socket.on('join_class', this.handleJoinClass.bind(this, socket))
      socket.on('raise_hand', this.handleRaiseHand.bind(this, socket))
      socket.on('chat_message', this.handleChatMessage.bind(this, socket))

      // Disconnect handler
      socket.on('disconnect', () => {
        Logger.info('User disconnected from WebSocket', {
          userId: user.id,
          action: 'websocket_disconnect',
          metadata: { socketId: socket.id }
        })
      })
    })
  }

  private handleJoinAssessment(socket: any, data: { assessmentId: string, sessionId: string }) {
    const user = socket.data.user as SocketUser
    const roomName = `assessment:${data.assessmentId}`

    socket.join(roomName)

    // Store session info
    this.assessmentSessions.set(data.sessionId, {
      sessionId: data.sessionId,
      userId: user.id,
      assessmentId: data.assessmentId,
      startTime: new Date(),
      endTime: new Date(Date.now() + 30 * 60 * 1000) // 30 minutes
    })

    // Start timer updates
    this.startAssessmentTimer(data.sessionId, roomName)

    Logger.info('User joined assessment', {
      userId: user.id,
      action: 'join_assessment',
      resource: `assessment:${data.assessmentId}`,
      metadata: { sessionId: data.sessionId }
    })
  }

  private startAssessmentTimer(sessionId: string, roomName: string) {
    const session = this.assessmentSessions.get(sessionId)
    if (!session) return

    const updateTimer = () => {
      const now = new Date()
      const timeRemaining = Math.max(0, Math.floor((session.endTime.getTime() - now.getTime()) / 1000))

      this.io.to(roomName).emit('time_update', {
        timeRemaining,
        warningAt: 300 // 5 minutes warning
      })

      if (timeRemaining <= 0) {
        // Auto-submit assessment
        this.io.to(roomName).emit('auto_submit', {
          message: 'Hết thời gian làm bài. Bài kiểm tra sẽ được tự động nộp.'
        })

        this.assessmentSessions.delete(sessionId)
        return
      }

      if (timeRemaining === 300) {
        // 5-minute warning
        this.io.to(roomName).emit('auto_submit_warning', {
          timeRemaining: 300,
          message: 'Còn 5 phút để hoàn thành bài kiểm tra'
        })
      }

      // Continue timer
      setTimeout(updateTimer, 1000)
    }

    updateTimer()
  }

  // Public methods for sending notifications
  public sendNotificationToUser(userId: string, notification: any) {
    this.io.to(`user:${userId}`).emit('notification', notification)
  }

  public sendNotificationToRole(role: string, notification: any) {
    this.io.emit('notification', notification) // Filter by role on client side
  }
}

// Initialize WebSocket service
let wsService: WebSocketService

export function initializeWebSocket(server: HTTPServer) {
  wsService = new WebSocketService(server)
  return wsService
}

export function getWebSocketService(): WebSocketService {
  if (!wsService) {
    throw new Error('WebSocket service not initialized')
  }
  return wsService
}
```

### 4.4 Payment Integration Patterns

**✅ Payment Service Pattern:**
```typescript
// lib/payment/stripeService.ts
import Stripe from 'stripe'

interface PaymentRequest {
  amount: number
  currency: string
  courseId: string
  userId: string
  metadata?: Record<string, string>
}

interface PaymentResult {
  paymentIntentId: string
  clientSecret: string
  status: string
}

export class StripeService {
  private stripe: Stripe

  constructor() {
    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
      apiVersion: '2023-10-16'
    })
  }

  async createPaymentIntent(request: PaymentRequest): Promise<PaymentResult> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: request.amount,
        currency: request.currency,
        metadata: {
          courseId: request.courseId,
          userId: request.userId,
          ...request.metadata
        },
        automatic_payment_methods: {
          enabled: true
        }
      })

      Logger.info('Payment intent created', {
        userId: request.userId,
        action: 'payment_intent_created',
        resource: `course:${request.courseId}`,
        metadata: {
          paymentIntentId: paymentIntent.id,
          amount: request.amount,
          currency: request.currency
        }
      })

      return {
        paymentIntentId: paymentIntent.id,
        clientSecret: paymentIntent.client_secret!,
        status: paymentIntent.status
      }
    } catch (error) {
      Logger.error('Payment intent creation failed', error, {
        userId: request.userId,
        action: 'payment_intent_failed',
        metadata: request
      })
      throw new Error('Không thể tạo payment intent')
    }
  }

  async handleWebhook(body: string, signature: string): Promise<void> {
    try {
      const event = this.stripe.webhooks.constructEvent(
        body,
        signature,
        process.env.STRIPE_WEBHOOK_SECRET!
      )

      switch (event.type) {
        case 'payment_intent.succeeded':
          await this.handlePaymentSuccess(event.data.object as Stripe.PaymentIntent)
          break

        case 'payment_intent.payment_failed':
          await this.handlePaymentFailed(event.data.object as Stripe.PaymentIntent)
          break

        default:
          Logger.warn(`Unhandled webhook event: ${event.type}`)
      }
    } catch (error) {
      Logger.error('Webhook handling failed', error)
      throw error
    }
  }

  private async handlePaymentSuccess(paymentIntent: Stripe.PaymentIntent) {
    const { courseId, userId } = paymentIntent.metadata

    try {
      // Enroll user in course
      await CourseService.enrollUserInCourse(userId, courseId)

      // Send confirmation email
      await EmailService.sendEnrollmentConfirmation(userId, courseId)

      // Send real-time notification
      const wsService = getWebSocketService()
      wsService.sendNotificationToUser(userId, {
        type: 'payment_success',
        title: 'Thanh toán thành công',
        message: 'Bạn đã được cấp quyền truy cập khóa học',
        courseId
      })

      Logger.info('Payment processed successfully', {
        userId,
        action: 'payment_success',
        resource: `course:${courseId}`,
        metadata: {
          paymentIntentId: paymentIntent.id,
          amount: paymentIntent.amount
        }
      })
    } catch (error) {
      Logger.error('Post-payment processing failed', error, {
        userId,
        action: 'post_payment_failed',
        resource: `course:${courseId}`,
        metadata: { paymentIntentId: paymentIntent.id }
      })
    }
  }
}
```

---

## 5. DEVELOPMENT WORKFLOW

### 5.1 Git Commit Message Format

**✅ Commit Message Convention:**
```
<type>(<scope>): <subject>

<body>

<footer>
```

**Types:**
- `feat`: Tính năng mới
- `fix`: Sửa bug
- `docs`: Cập nhật documentation
- `style`: Formatting, missing semicolons, etc.
- `refactor`: Code refactoring
- `test`: Thêm hoặc sửa tests
- `chore`: Maintenance tasks

**Examples:**
```bash
feat(auth): thêm tính năng đăng nhập bằng Google

- Tích hợp Google OAuth2
- Thêm button đăng nhập Google trong login form
- Cập nhật user model để lưu Google ID

Closes #123

fix(assessment): sửa lỗi chấm điểm AI không chính xác

- Cập nhật prompt cho OpenAI API
- Thêm validation cho audio input
- Sửa lỗi parse JSON response

refactor(course): tối ưu hóa query lấy danh sách khóa học

- Sử dụng aggregation pipeline thay vì multiple queries
- Thêm index cho performance
- Giảm response time từ 2s xuống 300ms
```

### 5.2 Code Review Checklist

**✅ Before Creating PR:**
- [ ] Code follows naming conventions
- [ ] All functions have proper TypeScript types
- [ ] Error handling is implemented
- [ ] Logging is added for important operations
- [ ] Performance considerations are addressed
- [ ] Tests are written and passing
- [ ] Documentation is updated

**✅ Code Review Points:**
- [ ] **Functionality**: Code works as expected
- [ ] **Readability**: Code is easy to understand
- [ ] **Performance**: No obvious performance issues
- [ ] **Security**: No security vulnerabilities
- [ ] **Error Handling**: Proper error handling
- [ ] **Testing**: Adequate test coverage
- [ ] **Documentation**: Comments and docs are clear

### 5.3 Testing Standards

**✅ Unit Test Pattern:**
```typescript
// __tests__/services/courseService.test.ts
import { CourseService } from '@/lib/database/services/courseService'
import { Course } from '@/lib/database/models/Course'
import { connectDB, disconnectDB } from '@/lib/database'

describe('CourseService', () => {
  beforeAll(async () => {
    await connectDB()
  })

  afterAll(async () => {
    await disconnectDB()
  })

  beforeEach(async () => {
    // Clean up test data
    await Course.deleteMany({})
  })

  describe('createCourse', () => {
    it('should create a new course successfully', async () => {
      // Arrange
      const courseData = {
        title: 'Test Course',
        description: 'Test Description',
        instructor: 'instructor_id',
        pricing: {
          type: 'paid' as const,
          price: 299000,
          currency: 'VND'
        }
      }

      // Act
      const result = await CourseService.createCourse(courseData)

      // Assert
      expect(result).toBeDefined()
      expect(result.title).toBe(courseData.title)
      expect(result.pricing.price).toBe(courseData.pricing.price)

      // Verify in database
      const savedCourse = await Course.findById(result._id)
      expect(savedCourse).toBeTruthy()
    })

    it('should throw error when required fields are missing', async () => {
      // Arrange
      const invalidData = {
        description: 'Test Description'
        // Missing required title
      }

      // Act & Assert
      await expect(CourseService.createCourse(invalidData as any))
        .rejects
        .toThrow('Không thể tạo khóa học')
    })
  })

  describe('getCourses', () => {
    beforeEach(async () => {
      // Seed test data
      await Course.create([
        {
          title: 'English Course',
          category: 'english',
          level: 'beginner',
          instructor: 'instructor1'
        },
        {
          title: 'Japanese Course',
          category: 'japanese',
          level: 'intermediate',
          instructor: 'instructor2'
        }
      ])
    })

    it('should return paginated courses', async () => {
      // Act
      const result = await CourseService.getCourses({
        page: 1,
        limit: 10
      })

      // Assert
      expect(result.courses).toHaveLength(2)
      expect(result.pagination.total).toBe(2)
      expect(result.pagination.page).toBe(1)
    })

    it('should filter courses by category', async () => {
      // Act
      const result = await CourseService.getCourses({
        category: 'english'
      })

      // Assert
      expect(result.courses).toHaveLength(1)
      expect(result.courses[0].category).toBe('english')
    })
  })
})
```

**✅ Integration Test Pattern:**
```typescript
// __tests__/api/courses.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/courses/route'

describe('/api/courses', () => {
  describe('GET', () => {
    it('should return courses list', async () => {
      // Arrange
      const { req, res } = createMocks({
        method: 'GET',
        query: {
          page: '1',
          limit: '10'
        }
      })

      // Act
      await handler(req, res)

      // Assert
      expect(res._getStatusCode()).toBe(200)

      const data = JSON.parse(res._getData())
      expect(data.success).toBe(true)
      expect(data.data).toHaveProperty('courses')
      expect(data.data).toHaveProperty('pagination')
    })
  })

  describe('POST', () => {
    it('should create new course when authenticated', async () => {
      // Arrange
      const courseData = {
        title: 'New Course',
        description: 'Course description',
        category: 'english',
        level: 'beginner'
      }

      const { req, res } = createMocks({
        method: 'POST',
        headers: {
          'authorization': 'Bearer valid_token'
        },
        body: courseData
      })

      // Act
      await handler(req, res)

      // Assert
      expect(res._getStatusCode()).toBe(201)

      const data = JSON.parse(res._getData())
      expect(data.success).toBe(true)
      expect(data.data.title).toBe(courseData.title)
    })

    it('should return 401 when not authenticated', async () => {
      // Arrange
      const { req, res } = createMocks({
        method: 'POST',
        body: { title: 'Test Course' }
      })

      // Act
      await handler(req, res)

      // Assert
      expect(res._getStatusCode()).toBe(401)
    })
  })
})
```

### 5.4 Documentation Requirements

**✅ README Template:**
```markdown
# Feature Name

## Mô tả
Brief description của feature bằng tiếng Việt.

## Technical Overview
Technical details in English.

## API Endpoints
- `GET /api/endpoint` - Description
- `POST /api/endpoint` - Description

## Database Schema
```typescript
interface ModelName {
  field: type
}
```

## Usage Examples
```typescript
// Code examples
```

## Testing
```bash
npm test -- feature-name
```

## Deployment Notes
Any special deployment considerations.
```

---

## KẾT LUẬN

### Quick Reference Checklist

**✅ Before Committing:**
- [ ] Code follows naming conventions
- [ ] TypeScript types are properly defined
- [ ] Error handling is implemented
- [ ] Logging is added
- [ ] Tests are written
- [ ] Documentation is updated

**✅ Code Quality Metrics:**
- **Readability**: Code should be self-documenting
- **Maintainability**: Easy to modify and extend
- **Performance**: No obvious bottlenecks
- **Security**: Input validation and error handling
- **Testability**: Functions are pure and testable

**✅ Team Standards:**
- **Language**: English for code, Vietnamese for business logic comments
- **Consistency**: Follow established patterns
- **Communication**: Clear commit messages and PR descriptions
- **Collaboration**: Code reviews are mandatory

### Resources
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Next.js Documentation](https://nextjs.org/docs)
- [React Best Practices](https://react.dev/learn)
- [MongoDB Best Practices](https://www.mongodb.com/developer/products/mongodb/mongodb-schema-design-best-practices/)

**Remember**: These guidelines evolve with the project. Update them as needed and ensure team alignment.
